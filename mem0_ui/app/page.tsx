"use client";

import React from 'react';

import Mem0StatsDashboard from "@/components/mem0/Mem0StatsDashboard";
import ActivityTimeline from "@/components/mem0/ActivityTimeline";

import { Toaster } from "@/components/ui/toaster";
import ErrorBoundary from "@/components/common/ErrorBoundary";

import "@/styles/animation.css";

export default function DashboardPage() {

  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-black text-white">
        <div className="container-responsive py-8">
          <div className="w-full mx-auto space-responsive">
            {/* Mem0 统计面板 */}
            <section className="animate-fade-slide-down" aria-labelledby="stats-heading">
              <h2 id="stats-heading" className="sr-only">系统统计概览</h2>
              <ErrorBoundary resetOnPropsChange>
                <Mem0StatsDashboard />
              </ErrorBoundary>
            </section>

            {/* 活动时间线 */}
            <section className="animate-fade-slide-down" aria-labelledby="timeline-heading">
              <h2 id="timeline-heading" className="sr-only">活动时间线</h2>
              <ErrorBoundary resetOnPropsChange>
                <ActivityTimeline />
              </ErrorBoundary>
            </section>


          </div>
        </div>

        <Toaster />
      </div>
    </ErrorBoundary>
  );
}
